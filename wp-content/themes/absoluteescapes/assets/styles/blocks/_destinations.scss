.destinations {

    a {
        text-decoration: none;

        &:hover, &:focus {
            text-decoration: none;
        }
    }

    &__inner {
        padding: 10px 0 30px 0;
    }

    &__country {
        text-align: left;
    }

    &__row {
        margin: 0 -15px;
    }

    &__col {
        width: 33.333333%;
        padding: 0 15px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            width: 260px;
            padding: 0 10px;

        }
    }

    &__carousel {
        padding-bottom: 38px;

        @media only screen and (max-width: map-get($grid-breakpoints-max, lg)) {
            padding-bottom: 30px;
        }
    }

    &__carousel-heading {
        margin-bottom: 15px;
    }

    &__item {
        position: relative;
        height: 300px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        display: flex;
        align-items: flex-end;

        // Add gradient overlay for text readability
        &::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60%;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            z-index: 1;
            pointer-events: none;
        }
    }

    &__link-text {
        margin: 0;
        transition: 300ms;
        color: $white;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    }

    &__link {
        display: block;
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: flex-end;
        text-decoration: none;

        &:hover, &:focus {
            text-decoration: none;

            .destinations__link-text {
                color: $teal;
            }
        }
    }

    &__content {
        position: relative;
        z-index: 2;
        padding: 20px;
        width: 100%;
    }


    .flickity-button {
        top: calc(50% - 12px);
        border-color: $blue;
        background: $blue;
        color: $white;
        box-shadow: 0 0 25px rgba($black, 0.35);

        &:hover, &:focus {
            border-color: $teal;
            background: $teal;
            color: $white;
        }

        &:disabled {
            opacity: 0;
        }

        &.previous {
            left: -20px;
        }

        &.next {
            right: -20px;
        }
    }
}